@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    html {
        direction: rtl;
    }

    html[dir="ltr"] {
        direction: ltr;
    }

    body {
        font-family: 'Cairo', 'Tajawal', 'Noto Sans Arabic', sans-serif;
    }
}

@layer components {
    .rtl-flip {
        transform: scaleX(-1);
    }

    .text-start-rtl {
        text-align: right;
    }

    .text-end-rtl {
        text-align: left;
    }

    .border-start-rtl {
        border-right-width: 1px;
        border-left-width: 0;
    }

    .border-end-rtl {
        border-left-width: 1px;
        border-right-width: 0;
    }

    .ps-rtl-3 {
        padding-right: 0.75rem;
        padding-left: 0;
    }

    .pe-rtl-4 {
        padding-left: 1rem;
        padding-right: 0;
    }

    .ms-rtl-1 {
        margin-right: 0.25rem;
        margin-left: 0;
    }

    .ms-rtl-2 {
        margin-right: 0.5rem;
        margin-left: 0;
    }

    .ms-rtl-3 {
        margin-right: 0.75rem;
        margin-left: 0;
    }

    .ms-rtl-4 {
        margin-right: 1rem;
        margin-left: 0;
    }

    .ms-rtl-6 {
        margin-right: 1.5rem;
        margin-left: 0;
    }

    .ms-rtl-10 {
        margin-right: 2.5rem;
        margin-left: 0;
    }

    .me-rtl-3 {
        margin-left: 0.75rem;
        margin-right: 0;
    }
}

import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
    ],

    theme: {
        extend: {
            fontFamily: {
                sans: ['Cairo', '<PERSON>jawal', '<PERSON>i', 'Noto Sans Arabic', ...defaultTheme.fontFamily.sans],
                arabic: ['Cairo', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', 'Noto Sans Arabic'],
            },
        },
    },

    plugins: [forms],
};

@props(['active'])

@php
$classes = ($active ?? false)
            ? 'block w-full pe-3 ps-4 py-2 border-r-4 border-indigo-400 text-start text-base font-medium text-indigo-700 bg-indigo-50 focus:outline-none focus:text-indigo-800 focus:bg-indigo-100 focus:border-indigo-700 transition duration-150 ease-in-out rtl:border-r-4 rtl:border-l-0'
            : 'block w-full pe-3 ps-4 py-2 border-r-4 border-transparent text-start text-base font-medium text-gray-600 hover:text-gray-800 hover:bg-gray-50 hover:border-gray-300 focus:outline-none focus:text-gray-800 focus:bg-gray-50 focus:border-gray-300 transition duration-150 ease-in-out rtl:border-r-4 rtl:border-l-0';
@endphp

<a {{ $attributes->merge(['class' => $classes]) }}>
    {{ $slot }}
</a>

{"accepted": "يجب قبول :attribute.", "accepted_if": "يجب قبول :attribute في حالة :other يساوي :value.", "active_url": "حقل :attribute لا يُمثّل رابطًا صحيحًا.", "after": "ي<PERSON><PERSON> على حقل :attribute أن يكون تاريخًا لاحقًا للتاريخ :date.", "after_or_equal": "حقل :attribute يجب أن يكون تاريخاً لاحقاً أو مطابقاً للتاريخ :date.", "alpha": "يجب أن لا يحتوي حقل :attribute سوى على حروف.", "alpha_dash": "يجب أن لا يحتوي حقل :attribute سوى على حروف، أرقام ومطّات.", "alpha_num": "يجب أن يحتوي حقل :attribute على حروفٍ وأرقامٍ فقط.", "any_of": "الحقل :attribute غير صالح.", "array": "يجب أن يكون حقل :attribute ًمصفوفة.", "ascii": "يجب أن يحتوي الحقل :attribute فقط على أحرف أبجدية رقمية أحادية البايت ورموز.", "attached": "حقل :attribute تم إرفاقه بالفعل.", "before": "ي<PERSON><PERSON> على حقل :attribute أن يكون تاريخًا سابقًا للتاريخ :date.", "before_or_equal": "حقل :attribute يجب أن يكون تاريخا سابقا أو مطابقا للتاريخ :date.", "between.array": "يج<PERSON> أن يحتوي حقل :attribute على عدد من العناصر بين :min و :max.", "between.file": "يجب أن يكون حجم ملف حقل :attribute بين :min و :max كيلوبايت.", "between.numeric": "يجب أن تكون قيمة حقل :attribute بين :min و :max.", "between.string": "يجب أن يكون عدد حروف نّص حقل :attribute بين :min و :max.", "boolean": "يجب أن تكون قيمة حقل :attribute إما true أو false .", "can": "الحقل :attribute يحتوي على قيمة غير مصرّح بها.", "confirmed": "حقل التأكيد غير مُطابق للحقل :attribute.", "contains": "الحقل :attribute يفتقد قيمة مطلوبة.", "current_password": "كلمة المرور غير صحيحة.", "date": "حقل :attribute ليس تاريخًا صحيحًا.", "date_equals": "يجب أن يكون حقل :attribute مطابقاً للتاريخ :date.", "date_format": "لا يتوافق حقل :attribute مع الشكل :format.", "decimal": "يجب أن يحتوي الحقل :attribute على :decimal منزلة/منازل عشرية.", "declined": "يجب رفض :attribute.", "declined_if": "يجب رفض :attribute عندما يكون :other بقيمة :value.", "different": "يجب أن يكون الحقلان :attribute و :other مُختلفين.", "digits": "يجب أن يحتوي حقل :attribute على :digits رقمًا/أرقام.", "digits_between": "يجب أن يحتوي حقل :attribute بين :min و :max رقمًا/أرقام .", "dimensions": "الحقل:attribute يحتوي على أبعاد صورة غير صالحة.", "distinct": "للحقل :attribute قيمة مُكرّرة.", "doesnt_end_with": "الحقل :attribute يجب ألّا ينتهي بأحد القيم التالية: :values.", "doesnt_start_with": "الحقل :attribute يجب ألّا يبدأ بأحد القيم التالية: :values.", "email": "يجب أن يكون حقل :attribute عنوان بريد إلكتروني صحيح البُنية.", "ends_with": "يجب أن ينتهي حقل :attribute بأحد القيم التالية: :values", "enum": "قيمة حقل :attribute غير موجودة في قائمة القيم المسموح بها.", "exists": "قيمة الحقل :attribute غير موجودة.", "extensions": "يجب أن يحتوي الحقل :attribute على أحد الإمتدادات التالية: :values.", "failed": "بيانات الاعتماد هذه غير متطابقة مع البيانات المسجلة لدينا.", "file": "الحقل :attribute يجب أن يكون ملفا.", "filled": "حقل :attribute إجباري.", "gt.array": "يج<PERSON> أن يحتوي حقل :attribute على أكثر من :value عناصر/عنصر.", "gt.file": "يجب أن يكون حجم ملف حقل :attribute أكبر من :value كيلوبايت.", "gt.numeric": "يجب أن تكون قيمة حقل :attribute أكبر من :value.", "gt.string": "يجب أن يكون طول نّص حقل :attribute أكثر من :value حروفٍ/حرفًا.", "gte.array": "يجب أن يحتوي حقل :attribute على الأقل على :value عُنصرًا/عناصر.", "gte.file": "يج<PERSON> أن يكون حجم ملف حقل :attribute على الأقل :value كيلوبايت.", "gte.numeric": "يجب أن تكون قيمة حقل :attribute مساوية أو أكبر من :value.", "gte.string": "يجب أن يكون طول نص حقل :attribute على الأقل :value حروفٍ/حرفًا.", "hex_color": "يجب أن يحتوي الحقل :attribute على صيغة لون HEX صالحة", "image": "يجب أن يكون حقل :attribute صورةً.", "in": "قيمة حقل :attribute غير موجودة في قائمة القيم المسموح بها.", "in_array": "حقل :attribute غير موجود في :other.", "in_array_keys": "يج<PERSON> أن يحتوي حقل :attribute على الأقل على أحد المفاتيح التالية: :values.", "integer": "يجب أن يكون حقل :attribute عددًا صحيحًا.", "ip": "يجب أن يكون حقل :attribute عنوان IP صحيحًا.", "ipv4": "يجب أن يكون حقل :attribute عنوان IPv4 صحيحًا.", "ipv6": "يجب أن يكون حقل :attribute عنوان IPv6 صحيحًا.", "json": "يجب أن يكون حقل :attribute نصًا من نوع JSON.", "list": "يجب أن يكون حقل :attribute قائمة.", "lowercase": "يجب أن يحتوي الحقل :attribute على حروف صغيرة.", "lt.array": "يجب أن يحتوي حقل :attribute على أقل من :value عناصر/عنصر.", "lt.file": "يجب أن يكون حجم ملف حقل :attribute أصغر من :value كيلوبايت.", "lt.numeric": "يجب أن تكون قيمة حقل :attribute أصغر من :value.", "lt.string": "يجب أن يكون طول نّص حقل :attribute أقل من :value حروفٍ/حرفًا.", "lte.array": "يجب أن لا يحتوي حقل :attribute على أكثر من :value عناصر/عنصر.", "lte.file": "يجب أن لا يتجاوز حجم ملف حقل :attribute :value كيلوبايت.", "lte.numeric": "يجب أن تكون قيمة حقل :attribute مساوية أو أصغر من :value.", "lte.string": "يجب أن لا يتجاوز طول نّص حقل :attribute :value حروفٍ/حرفًا.", "mac_address": "الحقل :attribute يجب أن يكون عنوان MAC صالحاً.", "max.array": "يجب أن لا يحتوي حقل :attribute على أكثر من :max عناصر/عنصر.", "max.file": "يجب أن لا يتجاوز حجم ملف حقل :attribute :max كيلوبايت.", "max.numeric": "يجب أن تكون قيمة حقل :attribute مساوية أو أصغر من :max.", "max.string": "يجب أن لا يتجاوز طول نّص حقل :attribute :max حروفٍ/حرفًا.", "max_digits": "يج<PERSON> ألا يحتوي الحقل :attribute على أكثر من :max رقم/أرقام.", "mimes": "يجب أن يكون ملفًا من نوع : :values.", "mimetypes": "يجب أن يكون ملفًا من نوع : :values.", "min.array": "يجب أن يحتوي حقل :attribute على الأقل على :min عُنصرًا/عناصر.", "min.file": "يجب أن يكون حجم ملف حقل :attribute على الأقل :min كيلوبايت.", "min.numeric": "يجب أن تكون قيمة حقل :attribute مساوية أو أكبر من :min.", "min.string": "يجب أن يكون طول نص حقل :attribute على الأقل :min حروفٍ/حرفًا.", "min_digits": "يجب أن يحتوي الحقل :attribute على الأقل :min رقم/أرقام.", "missing": "يجب أن يكون الحقل :attribute مفقوداً.", "missing_if": "يجب أن يكون الحقل :attribute مفقوداً عندما :other يساوي :value.", "missing_unless": "يجب أن يكون الحقل :attribute مفقوداً ما لم يكن :other يساوي :value.", "missing_with": "يجب أن يكون الحقل :attribute مفقوداً عند توفر :values.", "missing_with_all": "يجب أن يكون الحقل :attribute مفقوداً عند توفر :values.", "multiple_of": "حقل :attribute يجب أن يكون من مضاعفات :value", "next": "التالي &raquo;", "not_in": "يجب ألا يكون حقل :attribute موجودًا في القائمة.", "not_regex": "صيغة حقل :attribute غير صحيحة.", "numeric": "ي<PERSON><PERSON> على حقل :attribute أن يكون رقمًا.", "password": "كلمة المرور غير صحيحة.", "password.letters": "يج<PERSON> أن يحتوي حقل :attribute على حرف وا<PERSON><PERSON> على الأقل.", "password.mixed": "يجب أن يحتوي حقل :attribute على حرف كبير وحرف صغير على الأقل.", "password.numbers": "يجب أن يحتوي حقل :attribute على رقمٍ واحدٍ على الأقل.", "password.symbols": "يجب أن يحتوي حقل :attribute على رمزٍ واحدٍ على الأقل.", "password.uncompromised": "حقل :attribute ظهر في بيانات مُسربة. الرجاء اختيار :attribute مختلف.", "present": "يجب تقديم حقل :attribute.", "present_if": "يجب أن يكون الحقل :attribute متوفراً عندما :other يساوي :value.", "present_unless": "يجب أن يكون الحقل :attribute متوفراً ما لم يكن :other يساوي :value.", "present_with": "يجب أن يكون الحقل :attribute متوفراً عند توفر :values.", "present_with_all": "يجب أن يكون الحقل :attribute متوفراً عند توفر :values.", "previous": "&laquo; السابق", "prohibited": "حقل :attribute محظور.", "prohibited_if": "حقل :attribute محظور إذا كان :other هو :value.", "prohibited_if_accepted": "حقل :attribute محظور عند قبول الحقل :other.", "prohibited_if_declined": "حقل :attribute محظور عند رفض الحقل :other.", "prohibited_unless": "حقل :attribute محظور ما لم يكن :other ضمن :values.", "prohibits": "الحقل :attribute يحظر تواجد الحقل :other.", "regex": "صيغة حقل :attribute غير صحيحة.", "relatable": "حقل :attribute قد لا يكون مرتبطا بالمصدر المحدد.", "required": "حقل :attribute مطلوب.", "required_array_keys": "الحقل :attribute يجب أن يحتوي على مدخلات لـ: :values.", "required_if": "حقل :attribute مطلوب في حال ما إذا كان :other يساوي :value.", "required_if_accepted": "الحقل :attribute مطلوب عند قبول الحقل :other.", "required_if_declined": "الحقل :attribute مطلوب عند رفض الحقل :other.", "required_unless": "حقل :attribute مطلوب في حال ما لم يكن :other يساوي :values.", "required_with": "حقل :attribute مطلوب إذا توفّر :values.", "required_with_all": "حقل :attribute مطلوب إذا توفّر :values.", "required_without": "حقل :attribute مطلوب إذا لم يتوفّر :values.", "required_without_all": "حقل :attribute مطلوب إذا لم يتوفّر :values.", "reset": "تمت إعادة تعيين كلمة المرور!", "same": "يجب أن يتطابق حقل :attribute مع :other.", "sent": "تم إرسال تفاصيل استعادة كلمة المرور الخاصة بك إلى بريدك الإلكتروني!", "size.array": "يجب أن يحتوي حقل :attribute على :size عنصرٍ/عناصر بالضبط.", "size.file": "يجب أن يكون حجم ملف حقل :attribute :size كيلوبايت.", "size.numeric": "يجب أن تكون قيمة حقل :attribute مساوية لـ :size.", "size.string": "يجب أن يحتوي نص حقل :attribute على :size حروفٍ/حرفًا بالضبط.", "starts_with": "يج<PERSON> أن يبدأ حقل :attribute بأحد القيم التالية: :values", "string": "يجب أن يكون حقل :attribute نصًا.", "throttle": "عدد كبير جدا من محاولات الدخول. يرجى المحاولة مرة أخرى بعد :seconds ثانية.", "throttled": "الرجاء الانتظار قبل إعادة المحاولة.", "timezone": "يجب أن يكون حقل :attribute نطاقًا زمنيًا صحيحًا.", "token": "رمز استعادة كلمة المرور الذي أدخلته غير صحيح.", "ulid": "حقل :attribute يجب أن يكون بصيغة ULID سليمة.", "unique": "قيمة حقل :attribute مُستخدمة من قبل.", "uploaded": "فشل في تحميل الـ :attribute.", "uppercase": "يجب أن يحتوي الحقل :attribute على حروف كبيرة.", "url": "صيغة رابط حقل :attribute غير صحيحة.", "user": "لم يتم العثور على أيّ حسابٍ بهذا العنوان الإلكتروني.", "uuid": "حقل :attribute يجب أن يكون بصيغة UUID سليمة."}
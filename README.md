# نظام إدارة الموانئ

<p align="center">
<img src="https://img.shields.io/badge/Laravel-11.x-red.svg" alt="Laravel Version">
<img src="https://img.shields.io/badge/PHP-8.2+-blue.svg" alt="PHP Version">
<img src="https://img.shields.io/badge/License-MIT-green.svg" alt="License">
<img src="https://img.shields.io/badge/RTL-Supported-orange.svg" alt="RTL Support">
</p>

## حول النظام

نظام إدارة الموانئ هو تطبيق ويب شامل مبني باستخدام Laravel لإدارة عمليات الموانئ بكفاءة. يوفر النظام واجهة مستخدم عربية كاملة مع دعم RTL ومجموعة شاملة من الميزات لإدارة:

-   تسجيل وإدارة السفن
-   تتبع وإدارة الحاويات
-   مراقبة العمليات التشغيلية
-   إنشاء التقارير والإحصائيات
-   إدارة المستخدمين والصلاحيات

## الميزات الرئيسية

-   ✅ **دعم كامل للغة العربية**: واجهة مستخدم عربية بالكامل
-   ✅ **دعم RTL**: تخطيط من اليمين إلى اليسار
-   ✅ **خطوط عربية**: خطوط Cairo و Tajawal و Amiri
-   ✅ **تصميم متجاوب**: يعمل على جميع الأجهزة
-   ✅ **أمان عالي**: نظام مصادقة وتشفير متقدم

## متطلبات النظام

-   PHP 8.2 أو أحدث
-   Composer
-   Node.js & NPM
-   MySQL/PostgreSQL/SQLite
-   خادم ويب (Apache/Nginx)

## طريقة التركيب

### 1. استنساخ المشروع

```bash
git clone https://github.com/your-username/port-management.git
cd port-management
```

### 2. تركيب التبعيات

```bash
composer install
npm install
```

### 3. إعداد ملف البيئة

```bash
cp .env.example .env
php artisan key:generate
```

### 4. إعداد قاعدة البيانات

قم بتحديث ملف `.env` ببيانات قاعدة البيانات:

```env
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=port_management
DB_USERNAME=your_username
DB_PASSWORD=your_password
```

### 5. تشغيل الهجرات

```bash
php artisan migrate
```

### 6. بناء الأصول

```bash
npm run build
# أو للتطوير
npm run dev
```

### 7. تشغيل الخادم

```bash
php artisan serve
```

الآن يمكنك زيارة `http://localhost:8000` لرؤية النظام.

## الاستخدام

### إنشاء حساب مستخدم

1. انتقل إلى صفحة التسجيل
2. أدخل بياناتك الشخصية
3. قم بتأكيد بريدك الإلكتروني
4. ابدأ في استخدام النظام

## المساهمة

نرحب بمساهماتكم! يرجى قراءة [CONTRIBUTING.md](CONTRIBUTING.md) للحصول على التفاصيل.

## الرخصة

هذا المشروع مرخص تحت [MIT License](LICENSE).
